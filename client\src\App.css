/* Font Face Declarations */
@font-face {
  font-family: 'Absender';
  src: url('./fonts/TT Norms Pro Serif Trial DemiBold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Vividly';
  src: url('./fonts/TT Norms Pro Serif Trial Light.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

/* App Container */
.app-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(to bottom right, #1a1a1a, #000000);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Start from the top to ensure content is visible */
  padding: min(5vw, 1rem); /* Responsive padding */
  box-sizing: border-box; /* Include padding in width calculation */
  font-family: 'Vividly', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Authentication Styles */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  width: 100%;
  padding: 2rem 0;
}

.auth-form-container {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 2rem;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.auth-form-container h2 {
  font-family: 'Absender', sans-serif;
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
}

.auth-error {
  font-family: 'Vividly', sans-serif;
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid #F44336;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  text-align: center;
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-family: 'Absender', sans-serif;
  color: white;
  font-weight: bold;
}

.form-group input {
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 1rem;
}

.form-group input:focus {
  outline: none;
  border-color: #4fc3f7;
  box-shadow: 0 0 0 2px rgba(79, 195, 247, 0.3);
}

.auth-button {
  font-family: 'Absender', sans-serif;
  padding: 0.75rem;
  background-color: #cc5500;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-top: 0.5rem;
}

.auth-button:hover {
  background-color: #b84700;
}

.auth-button:disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

.auth-toggle {
  margin-top: 1.5rem;
  text-align: center;
}

.toggle-button {
  font-family: 'Absender', sans-serif;
  background: none;
  border: none;
  color: #cc5500;
  font-size: 0.9rem;
  cursor: pointer;
  text-decoration: underline;
}

.toggle-button:hover {
  color: #b84700;
}

.toggle-button:disabled {
  color: #b0bec5;
  cursor: not-allowed;
}

/* Dashboard Styles */
.dashboard-container {
  width: 100%;
  max-width: 800px;
  padding: 1rem;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.dashboard-header h1 {
  font-family: 'Absender', sans-serif;
  color: white;
  font-size: 1.8rem;
  margin: 0;
}

.logout-button {
  font-family: 'Absender', sans-serif;
  background-color: rgba(244, 67, 54, 0.2);
  color: white;
  border: 1px solid rgba(244, 67, 54, 0.5);
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(244, 67, 54, 0.3);
}

.dashboard-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.action-button {
  flex: 1;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.action-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Board Creation Section */
.board-creation-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  width: 100%;
  box-sizing: border-box;
}

.board-creation-section h2 {
  font-family: 'Absender', sans-serif;
  color: white;
  font-size: 1.5rem;
  margin: 0;
  text-align: center;
}

.start-playing-button {
  font-family: 'Absender', sans-serif;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-playing-button.enabled {
  background-color: #cc5500;
  color: white;
  box-shadow: 0 4px 15px rgba(204, 85, 0, 0.3);
}

.start-playing-button.enabled:hover {
  background-color: #b84700;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(204, 85, 0, 0.4);
}

.start-playing-button.disabled {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: none;
}

.start-playing-button:disabled {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
  box-shadow: none;
}

/* Agreement styles for modal */
.agreement-section {
  width: 100%;
  display: flex;
  justify-content: center;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  color: white;
  font-size: 1rem;
  line-height: 1.5;
  cursor: pointer;
  max-width: 500px;
  text-align: left;
}

.agreement-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
  accent-color: #4fc3f7;
  flex-shrink: 0;
  margin-top: 2px; /* Align with first line of text */
}

.agreement-checkbox a {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.2s ease;
}

.agreement-checkbox a:hover {
  color: #81d4fa;
  text-decoration: underline;
}

.users-section {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.users-section h2 {
  color: white;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.users-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.user-name {
  color: white;
  font-weight: bold;
}

.view-board-button {
  background-color: rgba(204, 85, 0, 0.2);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-board-button:hover {
  background-color: rgba(204, 85, 0, 0.3);
}

.no-users-message {
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  padding: 1rem;
}

/* Board Viewer Styles */
.board-viewer-container {
  width: 100%;
  max-width: 800px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.board-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.board-viewer-header h1 {
  font-family: 'Absender', sans-serif;
  color: rgba(255, 255, 255, 0.8);
  font-size: 2.2rem;
  margin: 0;
}

/* Your Board text styling */
.your-board-title {
  text-decoration: underline;
  opacity: 0.7; /* More transparent */
}

.back-button {
  font-family: 'Absender', sans-serif;
  background-color: rgba(204, 85, 0, 0.2);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin: 1.5rem auto;
  display: block;
  width: fit-content;
}

.back-button:hover {
  background-color: rgba(204, 85, 0, 0.3);
}

/* Read-only styles */
.bingo-square.read-only {
  cursor: default;
}

.bingo-square.read-only:hover {
  transform: none;
  box-shadow: none;
}

/* App Header */
.app-header {
  margin-bottom: 1rem;
  width: 100%;
  display: flex;
  justify-content: center;
}

.header-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 0.5rem 1rem;
  max-width: 800px;
  width: 100%;
}

.logo-container {
  flex-shrink: 0;
}

.title-logo {
  height: 80px;
  width: auto;
}

.thank-you-message {
  flex: 1;
}

.thank-you-message p {
  font-family: 'Vividly', sans-serif;
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

/* Main Navigation */
.main-navigation {
  margin-bottom: 2rem;
  width: 100%;
}

.nav-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  padding: 0.75rem 1rem;
  background-color: rgba(0, 0, 0, 0.15);
  border-radius: 25px;
  max-width: 800px;
  margin: 0 auto;
}

.nav-item {
  font-family: 'Absender', sans-serif;
  color: white;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  position: relative;
}

.nav-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 60%;
  background-color: rgba(255, 255, 255, 0.3);
}

.nav-item:hover {
  color: #cc5500;
  transform: translateY(-1px);
}

.nav-item.active {
  color: #cc5500;
  font-weight: bold;
}

/* Bingo Board */
.bingo-board {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: min(1.2vw, 0.5rem); /* Increased gap to prevent border overlap */
  padding: min(1vw, 0.5rem); /* Dynamic padding that scales with viewport */
  background-color: rgba(243, 244, 246, 0.1); /* More transparent background */
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: min(95vw, 95%); /* Use viewport width but cap at 95% */
  aspect-ratio: 1/1; /* Maintain square aspect ratio */
  max-height: 95vh; /* Ensure it doesn't exceed viewport height */
  max-width: 95vh; /* Keep it square by matching max-height */
}

/* Bingo Square */
.bingo-square {
  width: 95%; /* Slightly reduced from 100% to create space between squares */
  height: 95%; /* Slightly reduced from 100% to create space between squares */
  border: min(0.5vw, 3px) solid; /* Dynamic border that scales with viewport */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 0; /* Removed curved corners */
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  padding: 0;
  aspect-ratio: 1/1; /* Ensure square aspect ratio */
  margin: auto; /* Center the square in its grid cell */
}

/* Character info styles removed as they're no longer needed */

.thumbnail-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden; /* Ensure images don't overflow */
  cursor: pointer;
  position: relative; /* Added for absolute positioning of stars */
}

.character-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover; /* Cover the container while maintaining aspect ratio */
  object-position: center; /* Center the image */
  max-width: 100%; /* Ensure image doesn't exceed container width */
  max-height: 100%; /* Ensure image doesn't exceed container height */
  transition: transform 0.2s ease;
}

.thumbnail-container:hover .character-thumbnail {
  transform: scale(1.05);
}

.thumbnail-container:active .character-thumbnail {
  transform: scale(0.95);
}

/* Star rating system for rarity */
.rarity-stars {
  position: absolute;
  bottom: 5px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 2px;
  z-index: 4; /* Below the claimed overlay but above the thumbnail */
  pointer-events: none; /* Ensure clicks pass through to the thumbnail */
}

.star {
  font-size: 18px; /* Increased from 14px */
  filter: drop-shadow(0 0 2px rgba(0, 0, 0, 0.7)); /* Add shadow for better visibility */
}

/* Removed bingo-square:hover since borders are no longer clickable */

.bingo-square.marked {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  position: relative;
}

/* Claimed overlay with translucent black background and white check mark */
.bingo-square.marked::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.45); /* 45% opacity black overlay */
  z-index: 5; /* Lower z-index to ensure it doesn't block clicks */
  pointer-events: none; /* Ensure clicks pass through to the thumbnail */
}

.bingo-square.marked::after {
  content: 'CLAIMED';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(255, 255, 255, 0.7); /* Semi-transparent white checkmark */
  font-size: min(60%, 1.8rem); /* Increased font size */
  font-weight: bold;
  z-index: 6; /* Above the overlay but still low enough to not block clicks */
  pointer-events: none; /* Ensure clicks pass through to the thumbnail */
  text-align: center; /* Ensure text is centered */
  width: 100%; /* Take full width of container */
}

/* Special styling for the FREE square */
.bingo-square.free.marked::after {
  content: 'FREE';
  font-size: min(70%, 3.5rem); /* Increased font size */
}

/* Points and Controls Container */
.points-controls-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1.5rem; /* Increased gap for more spacing between meter and score */
  margin-top: min(4vh, 1.5rem);
  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */
  position: relative;
}

/* Controls wrapper for score display and refresh button */
.controls-wrapper {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Points Display */
.points-display {
  width: 100px;
  height: 100px;
  padding: min(2vw, 0.8rem);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  color: white;
  box-sizing: border-box; /* Include padding in width calculation */
  text-align: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.score-label {
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
  margin-bottom: 0.3rem;
}

.points-display .score-value {
  font-size: 2.2rem;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative; /* Ensure proper positioning */
}

/* Points Meter */
.points-meter-container {
  position: absolute;
  left: 0;
  height: 100px;
  width: calc(100% - 200px); /* Adjusted space for score and refresh button with wrapper */
  display: flex;
  align-items: center;
}

.points-meter {
  width: 100%;
  height: 100px; /* Match the height of the score container */
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  position: relative;
  margin: auto; /* Center vertically */
  overflow: hidden; /* Ensure fill is contained */
  display: flex;
  justify-content: center;
  align-items: center;
}

.points-meter-fill {
  height: 100%;
  background: white;
  border-radius: 15px;
  transition: width 0.5s ease-out;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.progress-text {
  font-size: 2.8rem; /* Bigger text */
  font-weight: 900; /* Extra bold for blockier appearance */
  text-transform: uppercase;
  letter-spacing: 1px; /* Tighter letter spacing to fit better */
  position: absolute;
  z-index: 2;
  mix-blend-mode: difference; /* Creates the negative color effect */
  color: rgba(255, 255, 255, 0.7); /* More translucent white for unfilled area */
  text-align: center;
  pointer-events: none; /* Ensures clicks pass through to the meter */
  width: 100%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-family: Arial, Helvetica, sans-serif; /* More blocky font */
  line-height: 1; /* Tighter line height */
}

/* Milestone markers (without tooltips) */
.milestone-marker {
  position: absolute;
  bottom: 0;
  transform: translateX(-50%);
  width: 2px;
  height: 100px; /* Match the height of the progress bar */
  z-index: 10;
}

.milestone-marker::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 2px;
  height: 15px; /* Small notch at the bottom */
  background-color: white; /* White notch for visibility */
  z-index: 10;
  pointer-events: none;
}

.milestone-value {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 0.9rem;
  white-space: nowrap;
  z-index: 12;
  pointer-events: none;
  width: max-content;
  text-align: center;
}

/* Next Reward Section */
.next-reward-section {
  margin-top: 1rem;
  text-align: center;
  color: white;
}

.next-reward-label {
  font-family: 'Absender', sans-serif;
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.next-reward-text {
  font-family: 'Vividly', sans-serif;
  font-size: 1.1rem;
  font-weight: bold;
  color: #4fc3f7;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Refresh Button */
.refresh-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.refresh-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: rotate(30deg);
}

.refresh-button:active {
  transform: rotate(60deg) scale(0.95);
}

/* Confirmation Modal */
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.confirmation-dialog {
  background-color: #2c3e50;
  border-radius: 8px;
  padding: 1.5rem;
  width: min(90%, 400px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  text-align: center;
  color: white;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.confirmation-dialog h3 {
  margin-top: 0;
  color: #f44336;
  font-size: 1.5rem;
}

.confirmation-dialog p {
  margin-bottom: 1.5rem;
  line-height: 1.5;
}

.confirmation-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.confirmation-buttons button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-button {
  background-color: #78909c;
  color: white;
}

.cancel-button:hover {
  background-color: #607d8b;
}

.confirm-button {
  background-color: #f44336;
  color: white;
}

.confirm-button:hover {
  background-color: #d32f2f;
}

.confirm-button.disabled {
  background-color: #b0bec5;
  cursor: not-allowed;
}

.confirm-button.disabled:hover {
  background-color: #b0bec5;
}

/* Agreement Modal */
.agreement-modal {
  background-color: #2c3e50;
  border-radius: 8px;
  padding: 2rem;
  width: min(90%, 500px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  text-align: center;
  color: white;
  animation: modal-appear 0.3s ease-out;
}

.agreement-modal h3 {
  margin-top: 0;
  color: #4fc3f7;
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
}

.agreement-text {
  margin-bottom: 2rem;
  text-align: left;
}

.agreement-text p {
  margin: 0;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
}

.agreement-text a {
  color: #4fc3f7;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.2s ease;
}

.agreement-text a:hover {
  color: #81d4fa;
  text-decoration: underline;
}

.agreement-checkbox-section {
  margin-bottom: 2rem;
  display: flex;
  justify-content: center;
}

.agreement-modal .agreement-checkbox {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: white;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  text-align: left;
}

.agreement-modal .agreement-checkbox input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
  accent-color: #4fc3f7;
  flex-shrink: 0;
}

/* Leaderboard Section */
.leaderboard-section {
  margin-top: min(4vh, 1.5rem);
  padding: min(5vw, 1.5rem);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */
  color: white;
  box-sizing: border-box; /* Include padding in width calculation */
}

.leaderboard-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 0.5rem;
}

.leaderboard-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.leaderboard-entry {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 0.5rem;
  transition: transform 0.2s ease;
}

.leaderboard-entry:hover {
  transform: translateX(5px);
  background-color: rgba(0, 0, 0, 0.3);
}

.user-rank {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  font-weight: bold;
  font-size: 1.2rem;
}

.user-name {
  width: 8rem; /* Increased width to accommodate 16 characters */
  font-weight: bold;
  font-size: 1.1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.score-bar-container {
  flex: 1;
  height: 2rem;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  cursor: pointer; /* Make it clickable */
  transition: background-color 0.2s ease;
}

.score-bar-container:hover {
  background-color: rgba(0, 0, 0, 0.4); /* Darker on hover */
}

.score-bar {
  height: 100%;
  background: white;
  border-radius: 1rem;
  transition: width 0.5s ease-out;
}

.score-number {
  width: 3rem;
  text-align: right;
  font-weight: bold;
  font-size: 1.1rem;
  color: white;
  flex-shrink: 0; /* Prevent shrinking */
}

/* Rules Section */
.rules-section {
  margin-top: min(4vh, 1.5rem);
  padding: min(5vw, 1.5rem);
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  width: min(95%, 800px); /* Use 95% of container width but cap at 800px */
  color: white;
  box-sizing: border-box; /* Include padding in width calculation */
}

.rules-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  padding-bottom: 0.5rem;
}

.rules-section p {
  font-size: 1rem;
  line-height: 1.6;
}

/* Dashboard Info Sections */
.dashboard-info-sections {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.rules-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Header Links */
.header-links {
  font-size: 0.9rem;
  margin-left: 0.5rem;
}

.header-links a {
  color: #61dafb;
  text-decoration: none;
  margin: 0 0.3rem;
  transition: color 0.2s ease;
}

.header-links a:hover {
  color: white;
  text-decoration: underline;
}

/* Standalone Rules Page */
.standalone-rules-page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  width: 100%;
  padding: 2rem 0;
}

/* How to Play Page - WikiHow Style */
.how-to-play-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 60vh;
  width: 100%;
  padding: 2rem 0;
  color: white;
}

.how-to-play-header {
  text-align: center;
  margin-bottom: 3rem;
  max-width: 900px;
}

.how-to-play-header h1 {
  font-family: 'Absender', sans-serif;
  font-size: 3rem;
  margin-bottom: 0.5rem;
  color: #4fc3f7;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.wikihow-subtitle {
  font-family: 'Vividly', sans-serif;
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.5rem;
  font-style: italic;
}

.wikihow-meta {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.wikihow-meta span {
  font-family: 'Absender', sans-serif;
  background-color: rgba(79, 195, 247, 0.2);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  border: 1px solid rgba(79, 195, 247, 0.3);
}

.how-to-play-content {
  width: min(95%, 1000px);
  box-sizing: border-box;
}

/* WikiHow Intro */
.wikihow-intro {
  background-color: rgba(255, 255, 255, 0.1);
  padding: 2rem;
  border-radius: 1rem;
  margin-bottom: 3rem;
  border-left: 5px solid #4fc3f7;
}

.wikihow-intro p {
  font-family: 'Vividly', sans-serif;
  font-size: 1.1rem;
  line-height: 1.7;
  margin: 0;
  color: rgba(255, 255, 255, 0.9);
}

/* WikiHow Steps */
.wikihow-steps {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.wikihow-step {
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: 1.5rem;
  padding: 2.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wikihow-step:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(79, 195, 247, 0.2);
}

.step-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.step-number {
  font-family: 'Absender', sans-serif;
  background: linear-gradient(135deg, #4fc3f7, #29b6f6);
  color: white;
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
  box-shadow: 0 4px 15px rgba(79, 195, 247, 0.4);
  flex-shrink: 0;
}

.step-title {
  font-family: 'Absender', sans-serif;
  font-size: 1.8rem;
  color: #4fc3f7;
  margin: 0;
  font-weight: bold;
}

.step-body {
  display: grid;
  gap: 2rem;
  align-items: center;
}

/* Alternating layout */
.wikihow-step.left .step-body {
  grid-template-columns: 1fr 300px;
}

.wikihow-step.right .step-body {
  grid-template-columns: 300px 1fr;
}

.wikihow-step.right .step-text {
  order: 2;
}

.wikihow-step.right .step-image {
  order: 1;
}

.step-text p {
  font-family: 'Vividly', sans-serif;
  font-size: 1.1rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.step-image {
  display: flex;
  justify-content: center;
  align-items: center;
}

.wikihow-image {
  max-width: 100%;
  height: auto;
  border-radius: 1rem;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
  border: 3px solid rgba(79, 195, 247, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.wikihow-image:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 35px rgba(79, 195, 247, 0.4);
}

/* WikiHow Conclusion */
.wikihow-conclusion {
  background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(139, 195, 74, 0.2));
  padding: 2.5rem;
  border-radius: 1.5rem;
  margin-top: 3rem;
  text-align: center;
  border: 2px solid rgba(76, 175, 80, 0.3);
}

.wikihow-conclusion h3 {
  font-family: 'Absender', sans-serif;
  font-size: 2rem;
  color: #81c784;
  margin-bottom: 1rem;
  font-weight: bold;
}

.wikihow-conclusion p {
  font-family: 'Vividly', sans-serif;
  font-size: 1.2rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

/* Responsive Design for WikiHow */
@media (max-width: 768px) {
  .how-to-play-header h1 {
    font-size: 2.2rem;
  }

  .wikihow-subtitle {
    font-size: 1.1rem;
  }

  .wikihow-meta {
    gap: 1rem;
  }

  .wikihow-meta span {
    font-family: 'Absender', sans-serif;
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .wikihow-step {
    padding: 1.5rem;
  }

  .step-number {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 1.2rem;
  }

  .step-title {
    font-size: 1.4rem;
  }

  /* Stack layout on mobile */
  .wikihow-step.left .step-body,
  .wikihow-step.right .step-body {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .wikihow-step.right .step-text,
  .wikihow-step.right .step-image {
    order: unset;
  }

  .step-text p {
    font-size: 1rem;
  }

  .wikihow-intro {
    padding: 1.5rem;
  }

  .wikihow-intro p {
    font-size: 1rem;
  }

  .wikihow-conclusion {
    padding: 1.5rem;
  }

  .wikihow-conclusion h3 {
    font-size: 1.6rem;
  }

  .wikihow-conclusion p {
    font-size: 1rem;
  }
}

/* Cards Component */
.cards-container {
  width: 100%;
  max-width: 1200px;
  padding: 1rem;
  color: white;
}

.cards-container h1 {
  font-family: 'Absender', sans-serif;
  text-align: center;
  color: #4fc3f7;
  font-size: 2.5rem;
  margin-bottom: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.cards-content {
  width: 100%;
}

.rarity-section {
  margin-bottom: 3rem;
}

.rarity-title {
  font-family: 'Absender', sans-serif;
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-align: center;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  padding: 0 1rem;
}

.card-item {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.card-item:hover {
  transform: translateY(-5px);
  background-color: rgba(255, 255, 255, 0.15);
  border-color: rgba(79, 195, 247, 0.5);
  box-shadow: 0 8px 25px rgba(79, 195, 247, 0.3);
}

.card-image-container {
  position: relative;
  margin-bottom: 0.75rem;
}

.card-thumbnail {
  width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 8px;
}

.card-frame {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  pointer-events: none;
}

.card-info {
  text-align: center;
}

.card-name {
  font-family: 'Absender', sans-serif;
  font-size: 1.1rem;
  font-weight: bold;
  color: white;
  margin: 0 0 0.25rem 0;
}

.card-source {
  font-family: 'Vividly', sans-serif;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.loading-message, .error-message {
  font-family: 'Vividly', sans-serif;
  text-align: center;
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  margin: 2rem 0;
}

.error-message {
  color: #f44336;
}

/* App Footer */
.app-footer {
  margin-top: 2rem;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.875rem;
}

/* Portrait Overlay */
.portrait-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(0px);
  transition: background-color 0.3s ease, backdrop-filter 0.3s ease;
}

.portrait-overlay.visible {
  background-color: rgba(0, 0, 0, 0.85);
  backdrop-filter: blur(5px);
}

.portrait-container {
  position: relative;
  max-width: 90%;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: scale(0.2);
  opacity: 0;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.3s ease;
}

.portrait-container.visible {
  transform: scale(1);
  opacity: 1;
}

.portrait-image-container {
  position: relative;
  display: inline-block;
}

.character-portrait {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 4px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
  display: block;
}

.portrait-frame-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
  pointer-events: none; /* Allow clicks to pass through to the portrait */
  z-index: 1; /* Ensure it appears above the portrait */
}

.close-button {
  position: absolute;
  top: -40px;
  right: -40px;
  width: 36px;
  height: 36px;
  background-color: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s;
  z-index: 1001;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.4);
}

.portrait-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
  width: 100%; /* Match the portrait width */
}

.claim-button {
  font-family: 'Absender', sans-serif;
  padding: 10px 20px;
  background-color: transparent;
  color: white;
  border: 2px solid white;
  border-radius: 4px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: none;
  position: relative;
  flex: 3; /* Take 3/4 of the available space */
}

.claim-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.8);
}

/* Unclaim button styling */
.claim-button.unclaim {
  background-color: transparent;
  color: #F44336;
  border-color: #F44336;
}

.claim-button.unclaim:hover {
  background-color: rgba(244, 67, 54, 0.1);
  border-color: rgba(244, 67, 54, 0.8);
}

.details-button {
  font-family: 'Absender', sans-serif;
  padding: 10px 15px;
  background-color: #2196F3; /* Default color, will be overridden by inline style */
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
  flex: 1; /* Take 1/4 of the available space */
  /* Glow effect will be applied via inline style */
}

.details-button:hover {
  transform: translateY(-1px);
  filter: brightness(1.1);
}

.character-details-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  border-radius: 4px;
  overflow: auto;
  cursor: pointer; /* Indicate it's clickable */
}

.character-details-content {
  color: white;
  padding: 30px;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: rgba(0, 0, 0, 0.9); /* Darker background for better readability */
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  cursor: default; /* Reset cursor for content area */
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.character-details-content p {
  font-family: 'Vividly', sans-serif;
  margin-bottom: 8px;
  line-height: 1.4;
  font-size: 1.3em; /* Increased from default */
}

.character-details-content p strong {
  font-family: 'Absender', sans-serif;
  color: #81d4fa; /* Light blue for headers - will be overridden by inline styles */
  font-size: 1.4em; /* Increased from 1.1em */
  display: block;
  margin-bottom: 2px;
}

/* Upload Overlay Styles */
.upload-overlay-content {
  text-align: center;
}

.upload-title {
  color: #fff;
  margin: 0 0 15px 0;
  font-size: 1.4em;
  font-family: 'Absender', sans-serif;
}

.upload-description {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 25px 0;
  font-size: 1.1em;
  font-family: 'vividly', sans-serif;
  line-height: 1.4;
}

.upload-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.upload-select-button,
.upload-confirm-button,
.upload-cancel-button {
  min-width: 180px;
  padding: 12px 24px;
  font-size: 1em;
  font-family: 'Absender', sans-serif;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: white;
}

.upload-select-button:hover:not(:disabled),
.upload-confirm-button:hover:not(:disabled),
.upload-cancel-button:hover:not(:disabled) {
  transform: translateY(-2px);
  filter: brightness(1.1);
}

.upload-select-button:disabled,
.upload-confirm-button:disabled,
.upload-cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.selected-file-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
  width: 100%;
}

.selected-file-info p {
  font-family: 'Vividly', sans-serif;
  margin-bottom: 2px;
  line-height: 1.4;
  font-size: 1.1em;
}

.selected-file-info p strong {
  font-family: 'Absender', sans-serif;
  font-size: 1.2em;
  display: block;
  margin-bottom: 2px;
}

.upload-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

/* Mobile adjustments for portrait overlay */
@media (max-width: 768px) {
  .portrait-container {
    max-width: 95%;
    transform: scale(0.1); /* Smaller initial scale on mobile for more dramatic effect */
  }

  .portrait-container.visible {
    transform: scale(1);
  }

  .close-button {
    top: -30px;
    right: -10px;
    width: 30px;
    height: 30px;
    font-size: 20px;
  }

  .portrait-buttons {
    flex-direction: row; /* Keep horizontal layout on mobile */
    gap: 8px;
    width: 100%;
  }

  .claim-button, .details-button {
    padding: 8px 16px;
    font-size: 16px;
  }

  .claim-button {
    border-width: 2px; /* Maintain border thickness on mobile */
    flex: 3; /* Maintain 3/4 ratio on mobile */
  }

  .details-button {
    flex: 1; /* Maintain 1/4 ratio on mobile */
    padding: 8px 10px; /* Smaller padding for smaller button */
  }

  .character-details-content {
    padding: 20px;
    font-size: 16px; /* Increased from 14px */
  }

  .character-details-content p {
    margin-bottom: 6px; /* Reduced spacing for mobile */
    font-size: 1.1em; /* Slightly smaller than desktop but still larger than before */
  }

  .character-details-content p strong {
    font-size: 1.2em; /* Adjusted for mobile */
    margin-bottom: 1px; /* Reduced spacing between header and text */
  }

  /* Faster transitions on mobile */
  .portrait-overlay {
    transition: background-color 0.25s ease, backdrop-filter 0.25s ease;
  }

  .portrait-container {
    transition: transform 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275), opacity 0.25s ease;
  }

  /* Upload overlay mobile adjustments */
  .upload-title {
    font-size: 1.2em;
  }

  .upload-description {
    font-size: 1em;
    margin-bottom: 20px;
  }

  .upload-buttons {
    gap: 12px;
  }

  .upload-actions {
    flex-direction: column;
    gap: 10px;
  }

  .upload-select-button,
  .upload-confirm-button,
  .upload-cancel-button {
    padding: 10px 20px;
    font-size: 0.9em;
    min-width: 150px;
  }
}

/* Loading and Error Messages */
.loading-message, .error-message {
  text-align: center;
  padding: 1rem;
  margin: 1rem;
  border-radius: 0.5rem;
}

.loading-message {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
}

.error-message {
  background-color: rgba(244, 67, 54, 0.1);
  color: #F44336;
  border: 1px solid #F44336;
}

/* Responsive Adjustments */
/* Small mobile devices in portrait mode */
@media (max-width: 320px) {
  .bingo-board {
    gap: 0.3rem; /* Increased minimal gap for very small screens */
    padding: 0.2rem;
  }

  .bingo-square {
    width: 94%; /* Slightly smaller squares on very small screens */
    height: 94%;
  }

  .bingo-square.marked::after {
    font-size: 1.2rem; /* Slightly larger font size for very small screens */
    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */
  }

  .bingo-square.free.marked::after {
    font-size: 2.2rem; /* Slightly larger size for very small screens */
  }

  /* Smaller stars for very small screens */
  .rarity-stars {
    bottom: 2px;
    gap: 1px;
  }

  .star {
    font-size: 14px; /* Increased from 10px */
  }
}

/* Mobile devices in portrait mode */
@media (orientation: portrait) {
  .header-row {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
  }

  .title-logo {
    height: 65px;
  }

  .thank-you-message p {
    font-size: 1rem;
  }

  .nav-container {
    padding: 0.5rem 0.75rem;
  }

  .nav-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .cards-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
  }

  .card-thumbnail {
    height: 120px;
  }

  .points-controls-container {
    margin-top: 1.5rem;
    gap: 0.5rem;
  }

  .controls-wrapper {
    gap: 0.5rem;
  }

  .points-display {
    width: 90px;
    height: 90px;
    padding: 0.8rem;
  }

  .score-label {
    font-size: 0.8rem;
  }

  .points-display .score-value {
    font-size: 1.8rem;
  }

  .refresh-button {
    width: 45px;
    height: 45px;
    font-size: 20px;
  }

  .points-meter-container {
    height: 90px;
    width: calc(100% - 160px); /* Adjusted for wrapper */
  }

  .points-meter {
    height: 90px;
  }

  .progress-text {
    font-size: 2rem; /* Bigger font size on mobile portrait but still fits */
  }

  .milestone-marker {
    bottom: 0;
    height: 90px;
  }

  .milestone-marker::before {
    height: 10px;
  }

  .milestone-value {
    font-size: 0.7rem;
    bottom: -20px;
  }

  .rules-section, .leaderboard-section {
    margin-top: 1.5rem;
    padding: 1rem;
  }

  .leaderboard-entry {
    padding: 0.4rem;
    gap: 0.5rem;
  }

  .user-rank {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 1rem;
  }

  .user-name {
    width: 6rem; /* Adjusted for mobile */
    font-size: 1rem;
  }

  .score-bar-container {
    height: 1.5rem;
  }

  .score-number {
    width: 2.5rem;
    font-size: 1rem;
  }
}

/* Mobile devices in landscape mode */
@media (orientation: landscape) and (max-height: 500px) {
  .app-container {
    padding: 0.5rem;
  }

  .app-header {
    margin-bottom: 0.5rem;
  }

  .main-navigation {
    margin-bottom: 1rem;
  }

  .title-logo {
    height: 55px;
  }

  .thank-you-message p {
    font-size: 0.9rem;
  }

  .nav-container {
    padding: 0.5rem 0.75rem;
  }

  .nav-item {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
  }

  .bingo-board {
    max-height: 80vh; /* Ensure it fits in landscape mode */
  }

  .points-controls-container {
    margin-top: 1rem;
    gap: 0.5rem;
  }

  .controls-wrapper {
    gap: 0.4rem;
  }

  .points-display {
    width: 80px;
    height: 80px;
    padding: 0.6rem;
  }

  .score-label {
    font-size: 0.7rem;
    margin-bottom: 0.2rem;
  }

  .points-display .score-value {
    font-size: 1.6rem;
  }

  .refresh-button {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .points-meter-container {
    height: 80px;
    width: calc(100% - 145px); /* Adjusted for wrapper */
  }

  .points-meter {
    height: 80px;
  }

  .progress-text {
    font-size: 1.8rem; /* Bigger font size on mobile landscape but still fits */
  }

  .milestone-marker {
    bottom: 0;
    height: 80px;
  }

  .milestone-marker::before {
    height: 10px;
  }

  .milestone-value {
    font-size: 0.65rem;
    bottom: -20px;
  }

  .rules-section, .leaderboard-section {
    margin-top: 1rem;
    padding: 0.8rem;
  }

  .leaderboard-container {
    gap: 0.5rem;
  }

  .leaderboard-entry {
    padding: 0.3rem;
    gap: 0.5rem;
  }

  .user-rank {
    width: 1.5rem;
    height: 1.5rem;
    font-size: 0.9rem;
  }

  .user-name {
    width: 5rem; /* Adjusted for mobile landscape */
    font-size: 0.9rem;
  }

  .score-bar-container {
    height: 1.5rem;
  }

  .score-number {
    width: 2rem;
    font-size: 0.9rem;
  }
}

/* Tablets and medium-sized screens */
@media (min-width: 768px) and (min-height: 768px) {
  .bingo-board {
    gap: min(1.5vw, 0.6rem); /* Increased gap for better visibility on tablets */
    padding: min(1.5vw, 0.6rem);
  }

  .bingo-square {
    width: 95%;
    height: 95%;
    border: min(0.6vw, 4px) solid;
  }

  .bingo-square.marked::after {
    font-size: 2rem; /* Increased font size for tablets */
    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */
  }

  .bingo-square.free.marked::after {
    font-size: 2.8rem; /* Increased size for tablets */
  }

  /* Larger stars for tablets */
  .rarity-stars {
    bottom: 8px;
    gap: 3px;
  }

  .star {
    font-size: 24px; /* Increased from 18px */
  }
}

/* Large screens and desktops */
@media (min-width: 1200px) {
  .bingo-board {
    max-width: 80vh; /* Slightly smaller on large screens for better aesthetics */
    max-height: 80vh;
    gap: 0.7rem; /* Fixed gap size for large screens */
  }

  .bingo-square {
    width: 95%;
    height: 95%;
    border: 4px solid;
  }

  .bingo-square.marked::after {
    font-size: 2.2rem; /* Increased font size for desktops */
    color: rgba(255, 255, 255, 0.7); /* Maintain semi-transparent white */
  }

  .bingo-square.free.marked::after {
    font-size: 3.5rem; /* Increased size for desktops */
  }

  /* Larger stars for big screens */
  .rarity-stars {
    bottom: 10px;
    gap: 4px;
  }

  .star {
    font-size: 28px; /* Increased from 22px */
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8));
  }
}
